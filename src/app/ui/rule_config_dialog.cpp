#include "rule_config_dialog.h"

#include <QGroupBox>
#include <QHeaderView>
#include <QJsonArray>
#include <QPushButton>
#include <QVBoxLayout>

#include "logging/logger.h"

NearConfigDialog::NearConfigDialog(const QString& title, QWidget* parent)
  : QDialog(parent) {
  setWindowTitle(title);
  setMinimumSize(500, 400);
  InitUI();
}

NearConfigDialog::~NearConfigDialog() {}

void NearConfigDialog::InitConfig(
    const QJsonArray& rule_array) {
  config_list_.clear();
  for (int i = 0; i < rule_array.size(); i++) {
    QJsonArray item = rule_array[i].toArray();
    if (item.size() == 3) {
      QString u1 = item[0].toString();
      QString u2 = item[1].toString();
      double dist = item[2].toDouble();
      config_list_.append(std::make_tuple(u1, u2, dist));
      LOG_INFO("InitConfig: {}", u1.toStdString());
    }
  }

  UpdateUI();
}

QJsonObject NearConfigDialog::GetConfig() {
  QJsonObject value;
  QJsonArray array;
  for (int i = 0; i < config_list_.size(); i++) {
    QJsonArray item;
    item.append(std::get<0>(config_list_[i]));
    item.append(std::get<1>(config_list_[i]));
    item.append(std::get<2>(config_list_[i]));
    array.append(item);
  }
  value["near"] = array;
  return value;
}

void NearConfigDialog::UpdateUI() {
  table_widget_->blockSignals(true); // 避免刷新时触发 cellChanged
  table_widget_->clearContents();
  table_widget_->setRowCount(0);

  table_widget_->setRowCount(config_list_.size());

  for (int row = 0; row < config_list_.size(); ++row) {
    const auto& item = config_list_[row];

    auto* keyItem = new QTableWidgetItem(std::get<0>(item));
    auto* valItem = new QTableWidgetItem(std::get<1>(item));
    auto* weightItem = new QTableWidgetItem(QString::number(std::get<2>(item)));

    table_widget_->setItem(row, 0, keyItem);
    table_widget_->setItem(row, 1, valItem);
    table_widget_->setItem(row, 2, weightItem);
  }
  table_widget_->blockSignals(false);
}

void NearConfigDialog::InitUI() {
  // 创建表格
  table_widget_ = new QTableWidget;
  table_widget_->setColumnCount(3);
  QStringList headers;
  headers << tr("Object1") << tr("Object2") << tr("MaxDistance");
  table_widget_->setHorizontalHeaderLabels(headers);
  table_widget_->horizontalHeader()->setSectionResizeMode(QHeaderView::Stretch);
  table_widget_->horizontalHeader()->setVisible(true);
  table_widget_->setSelectionBehavior(QAbstractItemView::SelectRows);
  table_widget_->setSelectionMode(QAbstractItemView::SingleSelection);
  table_widget_->setEditTriggers(QAbstractItemView::DoubleClicked |
                                 QAbstractItemView::EditKeyPressed);

  // 创建操作按钮
  add_btn_ = new QPushButton("Add");
  del_btn_ = new QPushButton("Delete");

  connect(add_btn_, &QPushButton::clicked, this, &NearConfigDialog::OnAddRow);
  connect(del_btn_, &QPushButton::clicked, this, &NearConfigDialog::OnDelRow);

  // 操作按钮布局
  QHBoxLayout* action_btn_layout = new QHBoxLayout();
  action_btn_layout->addWidget(add_btn_);
  action_btn_layout->addWidget(del_btn_);
  action_btn_layout->addStretch(); // 添加弹性空间

  // 创建确定/取消按钮
  button_box_ =
      new QDialogButtonBox(QDialogButtonBox::Ok | QDialogButtonBox::Cancel);
  connect(button_box_, &QDialogButtonBox::accepted, this, &QDialog::accept);
  connect(button_box_, &QDialogButtonBox::rejected, this, &QDialog::reject);

  // 主布局
  QVBoxLayout* main_layout = new QVBoxLayout();

  // 添加表格
  QGroupBox* table_group = new QGroupBox("Near Rules");
  QVBoxLayout* table_layout = new QVBoxLayout;
  table_layout->addWidget(table_widget_);
  table_group->setLayout(table_layout);
  main_layout->addWidget(table_group);

  // 添加操作按钮
  main_layout->addLayout(action_btn_layout);

  // 添加对话框按钮
  main_layout->addWidget(button_box_);

  setLayout(main_layout);
}

void NearConfigDialog::OnAddRow() {
  config_list_.append(std::make_tuple("", "", 0.0));
  UpdateUI();
  // 滚动到最后一行并进入编辑状态
  table_widget_->scrollToBottom();
  table_widget_->editItem(
      table_widget_->item(table_widget_->rowCount() - 1, 0));
}

void NearConfigDialog::OnDelRow() {
  int row = table_widget_->currentRow();
  if (row < 0) {
    return;
  }
  config_list_.remove(row);
  UpdateUI();
}
