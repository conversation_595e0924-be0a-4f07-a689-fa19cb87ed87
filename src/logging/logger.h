#ifndef LOGGING_LOGGER_H
#define LOGGING_LOGGER_H

#include <spdlog/spdlog.h>
#include <spdlog/sinks/stdout_color_sinks.h>
#include <spdlog/sinks/basic_file_sink.h>
#include <spdlog/sinks/rotating_file_sink.h>
#include <memory>
#include <string>

namespace logging {

/**
 * @brief 日志级别枚举
 */
enum class LogLevel {
  TRACE = 0,
  DEBUG = 1,
  INFO = 2,
  WARN = 3,
  ERR = 4,
  CRITICAL = 5,
  OFF = 6
};

/**
 * @brief 日志器封装类，支持显示代码行数
 */
class Logger {
public:
  /**
   * @brief 获取单例实例
   */
  static Logger& Instance();

  /**
   * @brief 初始化日志器
   * @param log_file_path 日志文件路径，为空则只输出到控制台
   * @param level 日志级别
   * @param max_file_size 单个日志文件最大大小（字节）
   * @param max_files 最大日志文件数量
   */
  void Initialize(const std::string& log_file_path = "",
                  LogLevel level = LogLevel::INFO,
                  size_t max_file_size = 1024 * 1024 * 10, // 10MB
                  size_t max_files = 3);

  /**
   * @brief 设置日志级别
   */
  void SetLevel(LogLevel level);

  /**
   * @brief 获取当前日志级别
   */
  LogLevel GetLevel() const;

  /**
   * @brief 记录 TRACE 级别日志
   */
  template <typename... Args>
  void Trace(const char* file, int line,
             const std::string& format, Args&&... args) {
    LogWithLocation(LogLevel::TRACE, file, line, format,
                    std::forward<Args>(args)...);
  }

  /**
   * @brief 记录 DEBUG 级别日志
   */
  template <typename... Args>
  void Debug(const char* file, int line,
             const std::string& format, Args&&... args) {
    LogWithLocation(LogLevel::DEBUG, file, line, format,
                    std::forward<Args>(args)...);
  }

  /**
   * @brief 记录 INFO 级别日志
   */
  template <typename... Args>
  void Info(const char* file, int line,
            const std::string& format, Args&&... args) {
    LogWithLocation(LogLevel::INFO, file, line, format,
                    std::forward<Args>(args)...);
  }

  /**
   * @brief 记录 WARN 级别日志
   */
  template <typename... Args>
  void Warn(const char* file, int line,
            const std::string& format, Args&&... args) {
    LogWithLocation(LogLevel::WARN, file, line, format,
                    std::forward<Args>(args)...);
  }

  /**
   * @brief 记录 ERROR 级别日志
   */
  template <typename... Args>
  void Error(const char* file, int line,
             const std::string& format, Args&&... args) {
    LogWithLocation(LogLevel::ERR, file, line, format,
                    std::forward<Args>(args)...);
  }

  /**
   * @brief 记录 CRITICAL 级别日志
   */
  template <typename... Args>
  void Critical(const char* file, int line,
                const std::string& format, Args&&... args) {
    LogWithLocation(LogLevel::CRITICAL, file, line, format,
                    std::forward<Args>(args)...);
  }

  /**
   * @brief 刷新日志缓冲区
   */
  void Flush();

private:
  Logger() = default;
  ~Logger() = default;
  Logger(const Logger&) = delete;
  Logger& operator=(const Logger&) = delete;

  /**
   * @brief 带位置信息的日志记录
   */
  template <typename... Args>
  void LogWithLocation(LogLevel level, const char* file, int line,

                       const std::string& format, Args&&... args) {
    if (!logger_ || level < current_level_) {
      return;
    }

    // 提取文件名（去掉路径）
    const char* filename = strrchr(file, '/');
    if (!filename) {
      filename = strrchr(file, '\\');
    }
    filename = filename ? filename + 1 : file;

    // 构造带位置信息的格式字符串
    std::string location_format = fmt::format("[{}:{}] {}", filename, line,
                                              format);

    // 根据级别调用相应的 spdlog 函数
    switch (level) {
      case LogLevel::TRACE:
        logger_->trace(location_format, std::forward<Args>(args)...);
        break;
      case LogLevel::DEBUG:
        logger_->debug(location_format, std::forward<Args>(args)...);
        break;
      case LogLevel::INFO:
        logger_->info(location_format, std::forward<Args>(args)...);
        break;
      case LogLevel::WARN:
        logger_->warn(location_format, std::forward<Args>(args)...);
        break;
      case LogLevel::ERR:
        logger_->error(location_format, std::forward<Args>(args)...);
        break;
      case LogLevel::CRITICAL:
        logger_->critical(location_format, std::forward<Args>(args)...);
        break;
      default:
        break;
    }
  }

  /**
   * @brief 转换日志级别
   */
  spdlog::level::level_enum ToSpdlogLevel(LogLevel level) const;

private:
  std::shared_ptr<spdlog::logger> logger_;
  LogLevel current_level_ = LogLevel::INFO;
  bool initialized_ = false;
};

} // namespace logging

// 便捷宏定义，自动获取文件名、行号和函数名
#define LOG_TRACE(format, ...) \
    logging::Logger::Instance().Trace(__FILE__, __LINE__, format, ##__VA_ARGS__)

#define LOG_DEBUG(format, ...) \
    logging::Logger::Instance().Debug(__FILE__, __LINE__, format, ##__VA_ARGS__)

#define LOG_INFO(format, ...) \
    logging::Logger::Instance().Info(__FILE__, __LINE__, format, ##__VA_ARGS__)

#define LOG_WARN(format, ...) \
    logging::Logger::Instance().Warn(__FILE__, __LINE__, format, ##__VA_ARGS__)

#define LOG_ERROR(format, ...) \
    logging::Logger::Instance().Error(__FILE__, __LINE__, format, ##__VA_ARGS__)

#define LOG_CRITICAL(format, ...) \
    logging::Logger::Instance().Critical(__FILE__, __LINE__, format, ##__VA_ARGS__)

#endif // LOGGING_LOGGER_H
